apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'
apply from: file("${rootDir}/config/library.gradle")

android {
    namespace "com.unis.reactivemvi"
    lintOptions {
        abortOnError false
    }

    buildFeatures{
        viewBinding true
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        // Ignore @OptIn warnings, Force 'SAM conversions = class' to compatible with AspectJ
        freeCompilerArgs = ["-opt-in=kotlin.RequiresOptIn", "-Xsam-conversions=class"]
        // Enable definitely non-nullable types
        languageVersion = "1.7"
        jvmTarget = "1.8"
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    api project(':platform')
    api project(':widget')
    api rootProject.ext.dependencies["title-bar"]
    implementation files('lib/ZSDK_ANDROID_API.jar')
}