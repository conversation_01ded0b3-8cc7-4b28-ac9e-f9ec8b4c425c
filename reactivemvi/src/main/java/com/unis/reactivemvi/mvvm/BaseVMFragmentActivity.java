package com.unis.reactivemvi.mvvm;

import android.content.Intent;
import android.os.Bundle;

import androidx.lifecycle.ViewModelProviders;

import com.customer.widget.photo.PhotoWidget;
import com.unis.reactivemvi.fragmentlauncher.SupportFragmentActivity;
import com.unis.reactivemvi.SnackType;
import com.unis.reactivemvi.Snacker;

import java.util.List;

public abstract class BaseVMFragmentActivity<VM extends BaseVM> extends SupportFragmentActivity implements BaseViewBehavior {

    protected VM viewModel;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        this.initViewModel();
        this.initBehaviorObserver();
    }


    public final void initViewModel() {
        BaseVM vm = this.createViewModel();
        viewModel = (VM) ViewModelProviders.of(this, BaseVM.createViewModelFactory(vm)).get(vm.getClass());
        getLifecycle().addObserver(viewModel);
    }

    protected abstract VM createViewModel();

    private void initBehaviorObserver() {
        viewModel.getLoadingEvent().observe(this, this::showLoading);
        viewModel.getCancelableLoadingEvent().observe(this,this::showCancelableLoading);
        viewModel.getToastEvent().observe(this, this::showToastByResId);
        viewModel.getPageNavigationEvent().observe(this, this::navigateTo);
        viewModel.getFinishPageEvent().observe(this, (isFinish) -> {
            if (isFinish != null && isFinish) finishPage();
        });
        viewModel.getSpeakAndToastEvent().observe(this, (message) -> {
            if (message != null && !message.isEmpty()) speakAndToast(message);
        });
        viewModel.getSpeakAndToastResEvent().observe(this, (res) -> {
            if (res != null) speakAndToast(res);
        });
        viewModel.getSpeakEvent().observe(this, (message) -> {
            if (message != null && !message.isEmpty()) speak(message);
        });
        viewModel.getVibrateEvent().observe(this, (event) -> {
            if (event != null) vibrate(event.second);
        });
        viewModel.getStartActivityEvent().observe(this, (params) -> {
            Class<?> clz = (Class<?>) params.get("CLASS");
            Bundle bundle = (Bundle) params.get("BUNDLE");
            Intent intent = new Intent(this, clz);
            if (bundle != null) {
                intent.putExtras(bundle);
            }
            startActivity(intent);
        });
        viewModel.getSnackEvent().observe(this, (event) -> {
            Snacker.show(this, event.first, event.second);
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        getLifecycle().removeObserver(viewModel);
    }

    @Override
    public void showLoading(boolean isShow) {
        this.showProgress(isShow, false);
    }

    @Override
    public void showCancelableLoading(boolean isShow) {
        this.showProgress(isShow, true);
    }

    @Override
    public void showToastByResId(int messageResId) {
        showToast(messageResId);
    }

    @Override
    public void navigateTo(Intent intent) {
        startActivity(intent);
    }

    @Override
    public void finishPage() {
        finish();
    }

    @Override
    public void addPhotoWidget(PhotoWidget widget) {
        super.addPhotoWidget(widget);
    }

    @Override
    public List<PhotoWidget> getPhotoWidgets() {
        return super.getPhotoWidgets();
    }

    @Override
    public void setCurtActivePhotoWidget(int widgetId) {
        super.setCurtActivePhotoWidget(widgetId);
    }

    @Override
    public void showSnackSuccess(String message) {
        Snacker.show(this, message, new SnackType.SuccessV1());
    }

    @Override
    public void showSnackDelete(String message) {
        Snacker.show(this, message, new SnackType.SuccessV1());
    }
}
