package com.unis.reactivemvi.mvvm;

import android.content.Intent;
import android.os.Bundle;

import androidx.fragment.app.Fragment;
import androidx.core.util.Pair;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;

import com.linc.platform.common.handler.DoneHandler;
import com.linc.platform.common.handler.FailedHandler;
import com.linc.platform.common.handler.SuccessHandler;
import com.linc.platform.http.ErrorCodeSubscriber;
import com.linc.platform.http.ErrorResponse;
import com.linc.platform.utils.RxUtil;
import com.linc.platform.utils.ToastUtil;
import com.unis.reactivemvi.fragmentlauncher.FragmentLaunchModel;
import com.unis.reactivemvi.SnackType;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Response;
import io.reactivex.rxjava3.core.Observable;



/**
 * <AUTHOR>
 * @date 2020/11/30
 * @desc Base viewModel
 * Deprecated. Use Kotlin version BaseViewModel instead.
 */
@Deprecated
public abstract class BaseVM extends ViewModel implements BaseViewBehavior, VMLifeCycle {

    private final MutableLiveData<Boolean> loadingEvent = new MutableLiveData<>();
    private final MutableLiveData<Boolean> cancelableLoadingEvent = new MutableLiveData<>();
    private final MutableLiveData<Integer> toastEvent = new MutableLiveData<>();
    private final MutableLiveData<Intent> pageNavigationEvent = new MutableLiveData<>();
    private final MutableLiveData<Boolean> finishPageEvent = new MutableLiveData<>();
    private final MutableLiveData<String> speakAndToastEvent = new MutableLiveData<>();
    private final MutableLiveData<Integer> speakAndToastResEvent = new MutableLiveData<>();
    private final MutableLiveData<String> speakEvent = new MutableLiveData<>();
    private final MutableLiveData<Pair<Integer, Long>> vibrateEvent = new MutableLiveData<>();
    private final MutableLiveData<Map<String, Object>> startActivityEvent = new MutableLiveData<>();
    private final MutableLiveData<String> snackerSuccessEvent = new MutableLiveData<>();
    private final MutableLiveData<String> snackerDeleteEvent = new MutableLiveData<>();
    private final MutableLiveData<Pair<String, SnackType>> snackEvent = new MutableLiveData<>();
    private final MutableLiveData<Map<String, Object>> startFragmentEvent = new MutableLiveData<>();

    public static <T extends BaseVM> ViewModelProvider.Factory createViewModelFactory(T viewModel) {
        return new VMFactory(viewModel);
    }

    public final MutableLiveData<Boolean> getLoadingEvent() {
        return this.loadingEvent;
    }

    public final MutableLiveData<Boolean> getCancelableLoadingEvent() {
        return this.cancelableLoadingEvent;
    }

    public final MutableLiveData<Integer> getToastEvent() {
        return this.toastEvent;
    }

    public final MutableLiveData<Intent> getPageNavigationEvent() {
        return this.pageNavigationEvent;
    }

    public final MutableLiveData<Boolean> getFinishPageEvent() {
        return this.finishPageEvent;
    }

    public final MutableLiveData<String> getSpeakAndToastEvent() {
        return this.speakAndToastEvent;
    }

    public final MutableLiveData<Integer> getSpeakAndToastResEvent() {
        return this.speakAndToastResEvent;
    }

    public final MutableLiveData<String> getSpeakEvent() {
        return this.speakEvent;
    }

    public MutableLiveData<Pair<Integer, Long>> getVibrateEvent() {
        return this.vibrateEvent;
    }

    public MutableLiveData<Map<String, Object>> getStartActivityEvent() {
        return startActivityEvent;
    }

    public final MutableLiveData<String> getSnackerSuccessEvent() {
        return this.snackerSuccessEvent;
    }

    public final MutableLiveData<String> getSnackerDeleteEvent() {
        return this.snackerDeleteEvent;
    }

    public MutableLiveData<Map<String, Object>> getStartFragmentEvent() {
        return startFragmentEvent;
    }

    public MutableLiveData<Pair<String, SnackType>> getSnackEvent() {
        return this.snackEvent;
    }

    public void vibrate() {
        this.vibrate(250L);
    }

    public void vibrate(Long duration) {
        Pair<Integer, Long> oldValue = vibrateEvent.getValue();
        Integer newCount = oldValue == null ? 0 : oldValue.first + 1;
        Pair<Integer, Long> newValue = Pair.create(newCount, duration);
        vibrateEvent.postValue(newValue);
    }


    public void showLoading(boolean isShow) {
        this.loadingEvent.postValue(isShow);
    }

    public void showCancelableLoading(boolean isShow) {
        this.cancelableLoadingEvent.postValue(isShow);
    }

    public void showToastByResId(int resId) {
        this.toastEvent.postValue(resId);
    }

    public void navigateTo(Intent intent) {
        this.pageNavigationEvent.postValue(intent);
    }

    public void finishPage() {
        this.finishPageEvent.postValue(true);
    }

    public void speakAndToast(String message) {
        this.speakAndToastEvent.postValue(message);
    }

    public void speakAndToast(Integer messageRes) {
        this.speakAndToastResEvent.postValue(messageRes);
    }

    public void speak(String message) {
        this.speakEvent.postValue(message);
    }

    /**
     * 跳转页面
     *
     * @param clz    所跳转的目的Activity类
     * @param bundle 跳转所携带的信息
     */
    public void startActivity(Class<?> clz, Bundle bundle) {
        Map<String, Object> params = new HashMap<>();
        params.put("CLASS", clz);
        if (bundle != null) {
            params.put("BUNDLE", bundle);
        }
        startActivityEvent.postValue(params);
    }

    public void showSnackSuccess(String message) {
        snackerSuccessEvent.postValue(message);
    }

    public void showSnackDelete(String message) {
        snackerDeleteEvent.postValue(message);
    }

    public void startFragment(Fragment fragment) {
        startFragment(fragment, FragmentLaunchModel.DEFAULT);
    }

    public void startFragment(Fragment fragment, FragmentLaunchModel launchModel) {
        Map<String, Object> params = new HashMap<>();
        params.put("fragment", fragment);
        if (launchModel != null) {
            params.put("launchModel", launchModel);
        }
        startFragmentEvent.postValue(params);
    }

    public void showSnack(SnackType type, String message) {
        Pair<String, SnackType> value =  Pair.create(message, type);
        snackEvent.postValue(value);
    }




    public void onAny(LifecycleOwner owner, Lifecycle.Event event) {
    }

    public void onCreate() {
    }

    public void onStart() {
    }

    public void onResume() {
    }

    public void onPause() {
    }

    public void onStop() {
    }

    public void onDestroy() {
    }

    protected <T> void executeWithoutLoading(Observable<Response<T>> observable,
                                             SuccessHandler<T> successHandler) {
        this.executeWithoutLoading(observable, successHandler, null, null);
    }

    protected <T> void executeWithoutLoading(Observable<Response<T>> observable,
                                             SuccessHandler<T> successHandler,
                                             DoneHandler doneHandler) {
        this.executeWithoutLoading(observable, successHandler, null, doneHandler);
    }

    protected <T> void executeWithoutLoading(Observable<Response<T>> observable,
                                             SuccessHandler<T> successHandler,
                                             FailedHandler<ErrorResponse> failedHandler) {
        this.executeWithoutLoading(observable, successHandler, failedHandler, null);
    }

    protected <T> void executeWithoutLoading(Observable<Response<T>> observable,
                                             SuccessHandler<T> successHandler,
                                             FailedHandler<ErrorResponse> failedHandler,
                                             DoneHandler doneHandler) {
        this.execute(observable, successHandler, failedHandler, doneHandler, false, false);
    }

    protected <T> void executeWithLoading(Observable<Response<T>> observable,
                                          SuccessHandler<T> successHandler) {
        this.executeWithLoading(observable, successHandler, null, null, true);
    }

    protected <T> void executeWithLoading(Observable<Response<T>> observable,
                                          SuccessHandler<T> successHandler,
                                          boolean loadingCancelable) {
        this.executeWithLoading(observable, successHandler, null, null, loadingCancelable);
    }

    protected <T> void executeWithLoading(Observable<Response<T>> observable,
                                          SuccessHandler<T> successHandler,
                                          DoneHandler doneHandler) {
        this.executeWithLoading(observable, successHandler, null, doneHandler, true);
    }

    protected <T> void executeWithLoading(Observable<Response<T>> observable,
                                          SuccessHandler<T> successHandler,
                                          DoneHandler doneHandler,
                                          boolean loadingCancelable
    ) {
        this.executeWithLoading(observable, successHandler, null, doneHandler, loadingCancelable);
    }

    protected <T> void executeWithLoading(Observable<Response<T>> observable,
                                          SuccessHandler<T> successHandler,
                                          FailedHandler<ErrorResponse> failedHandler) {
        this.executeWithLoading(observable, successHandler, failedHandler, null, true);
    }

    protected <T> void executeWithLoading(Observable<Response<T>> observable,
                                          SuccessHandler<T> successHandler,
                                          FailedHandler<ErrorResponse> failedHandler,
                                          boolean loadingCancelable) {
        this.executeWithLoading(observable, successHandler, failedHandler, null, loadingCancelable);
    }


    protected <T> void executeWithLoading(Observable<Response<T>> observable,
                                          SuccessHandler<T> successHandler,
                                          FailedHandler<ErrorResponse> failedHandler,
                                          DoneHandler doneHandler,
                                          boolean loadingCancelable) {
        this.execute(observable, successHandler, failedHandler, doneHandler, true, loadingCancelable);
    }

    protected <T> void executeAndShowProgressByParam(Observable<Response<T>> observable,
                                                     SuccessHandler<T> successHandler,
                                                     boolean showLoading) {
        this.execute(observable, successHandler, null, null, showLoading, showLoading);
    }

    protected <T> void executeAndShowProgressByParam(Observable<Response<T>> observable,
                                                     SuccessHandler<T> successHandler,
                                                     DoneHandler doneHandler,
                                                     boolean showLoading) {
        this.execute(observable, successHandler, null, doneHandler, showLoading, showLoading);
    }

    private <T> void execute(Observable<Response<T>> observable,
                             SuccessHandler<T> successHandler,
                             FailedHandler<ErrorResponse> failedHandler,
                             DoneHandler doneHandler,
                             boolean showLoading,
                             boolean loadingCancelable
    ) {
        if (showLoading) {
            if (loadingCancelable) {
                showCancelableLoading(true);
            } else {
                showLoading(true);
            }
        }

        observable.compose(RxUtil.asyncSchedulers()).subscribe(new ErrorCodeSubscriber<Response<T>>() {
            @Override
            public void onSuccess(Response<T> tResponse) {
                if (successHandler != null) successHandler.onSuccess(tResponse.body());
            }

            @Override
            public void onFailed(ErrorResponse errorResponse) {
                if (failedHandler == null) {
                    ToastUtil.showToast(errorResponse.error);
                } else {
                    failedHandler.onFailed(errorResponse);
                }
            }

            @Override
            public void onDone() {
                if (doneHandler != null) doneHandler.onDone();
                if (showLoading) {
                    if (loadingCancelable) {
                        showCancelableLoading(false);
                    } else {
                        showLoading(false);
                    }
                }
            }
        });
    }

    final static class VMFactory implements ViewModelProvider.Factory {
        private final BaseVM VM;

        public <T extends ViewModel> T create(Class<T> modelClass) {
            return (T) VM;
        }

        public VMFactory(BaseVM VM) {
            this.VM = VM;
        }
    }

}


