package com.unis.reactivemvi.mvvm;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Vibrator;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.lifecycle.ViewModelProviders;

import com.linc.platform.utils.ToastUtil;
import com.unis.reactivemvi.SnackType;
import com.unis.reactivemvi.Snacker;
import com.unis.reactivemvi.fragmentlauncher.FragmentLaunchModel;
import com.unis.reactivemvi.fragmentlauncher.SupportFragment;

/**
 * <AUTHOR>
 * @date 2020/12/3
 * @desc Base ViewModel Fragment
 * Deprecated. Use Kotlin version BaseVMFragment instead.
 */
@Deprecated
public abstract class BaseVMFragment<VM extends BaseVM> extends SupportFragment implements BaseViewBehavior {
    protected VM viewModel;
    private boolean isPrepared = false;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    protected void initView() {
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        this.initViewModel();
        this.initBehaviorObserver();
        this.isPrepared = true;
        return super.onCreateView(inflater, container, savedInstanceState);
    }

    private void onVisibleChanged(boolean isVisible) {
        if (isVisible) onVisible();
        else onInVisible();
    }

    public void onVisible() {
    }

    public void onInVisible() {
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (!isPrepared) return;
        onVisibleChanged(isVisibleToUser);
    }

    public final void initViewModel() {
        VM vm = this.createViewModel();
        viewModel = (VM) ViewModelProviders.of(this, BaseVM.createViewModelFactory(vm)).get(vm.getClass());
        getLifecycle().addObserver(viewModel);
    }

    protected abstract VM createViewModel();

    public final VM getViewModel() {
        return viewModel;
    }


    public final <vm extends BaseVM> vm getActivityViewModel(Class<vm> vmClass) {
        if (this.getActivity() != null) {
            return ViewModelProviders.of(getActivity()).get(vmClass);
        } else {
            return null;
        }
    }

    private void initBehaviorObserver() {
        viewModel.getLoadingEvent().observe(this, this::showLoading);
        viewModel.getCancelableLoadingEvent().observe(this, this::showCancelableLoading);
        viewModel.getToastEvent().observe(this, this::showToastByResId);
        viewModel.getPageNavigationEvent().observe(this, this::navigateTo);
        viewModel.getFinishPageEvent().observe(this, (isFinish) -> {
            if (isFinish != null && isFinish) finishPage();
        });
        viewModel.getSpeakAndToastEvent().observe(this, (message) -> {
            if (message != null && !message.isEmpty()) speakAndToast(message);
        });
        viewModel.getSpeakAndToastResEvent().observe(this, (res) -> {
            if (res != null) speakAndToast(res);
        });
        viewModel.getSpeakEvent().observe(this, (message) -> {
            if (message != null && !message.isEmpty()) speak(message);
        });
        viewModel.getVibrateEvent().observe(this, (event) -> {
            if (event != null) vibrate(event.second);
        });
        viewModel.getStartActivityEvent().observe(this, (params) -> {
            Class<?> clz = (Class<?>) params.get("CLASS");
            Bundle bundle = (Bundle) params.get("BUNDLE");
            Intent intent = new Intent(getActivity(), clz);
            if (bundle != null) {
                intent.putExtras(bundle);
            }
            startActivity(intent);
        });
        viewModel.getSnackerSuccessEvent().observe(this, this::showSnackSuccess);
        viewModel.getSnackerDeleteEvent().observe(this, this::showSnackDelete);
        viewModel.getStartFragmentEvent().observe(this, (params) -> {
            SupportFragment fragment = (SupportFragment) params.get("fragment");
            FragmentLaunchModel launchModel = (FragmentLaunchModel) params.get("launchModel");
            if (launchModel == null) {
                launchModel = FragmentLaunchModel.DEFAULT;
            }
            startFragment(fragment, launchModel);
        });
    }

    protected void vibrate(Long duration) {
        if (this.getContext() == null) return;
        Vibrator vibrator = (Vibrator) this.getContext().getSystemService(Context.VIBRATOR_SERVICE);
        vibrator.vibrate(duration);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        getLifecycle().removeObserver(viewModel);
    }

    @Override
    public void showLoading(boolean isShow) {
        this.showProgress(isShow, false);
    }

    @Override
    public void showCancelableLoading(boolean isShow) {
        this.showProgress(isShow, true);
    }

    @Override
    public void showToastByResId(int messageResId) {
        ToastUtil.showToast(messageResId);
    }

    @Override
    public void showSnackSuccess(String message) {
         Snacker.show(getActivity(), message, new SnackType.SuccessV1());
    }

    @Override
    public void showSnackDelete(String message) {
        Snacker.show(getActivity(), message, new SnackType.SuccessV1());
    }

    @Override
    public void navigateTo(Intent intent) {
        startActivity(intent);
    }

    @Override
    public void finishPage() {
        if (getActivity() != null) {
            getActivity().finish();
        }
    }

}
