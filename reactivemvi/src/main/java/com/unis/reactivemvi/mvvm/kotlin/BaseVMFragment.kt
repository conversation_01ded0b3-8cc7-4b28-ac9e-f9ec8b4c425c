package com.unis.reactivemvi.mvvm.kotlin

import android.content.Context
import android.os.Bundle
import android.os.Vibrator
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProviders
import androidx.viewbinding.ViewBinding
import com.customer.widget.hideIfShowing
import com.linc.platform.utils.ResUtil
import com.linc.platform.utils.ToastUtil
import com.unis.reactivemvi.mvvm.kotlin.androidx.lifecycle.runtime_ktx.LifecycleScopeRef
import com.unis.reactivemvi.mvvm.kotlin.androidx.lifecycle.runtime_ktx.ViewLifecycleScopeRef
import com.unis.reactivemvi.mvvm.kotlin.extensions.ParamViewModelFactory
import com.unis.reactivemvi.mvvm.kotlin.extensions.getVmClazz
import com.unis.reactivemvi.mvvm.kotlin.extensions.inflateBindingWithGeneric
import com.unis.reactivemvi.SnackType
import com.unis.reactivemvi.Snacker
import com.unis.reactivemvi.fragmentlauncher.SupportFragment
import java.util.concurrent.atomic.AtomicReference

/**
 * <AUTHOR>
 * @date 2021/10
 * @desc Base Fragment in kotlin
 */

abstract class BaseFragment : SupportFragment(), LifecycleScopeRef, ViewLifecycleScopeRef {

    override var mInternalScopeRef: AtomicReference<Any> = AtomicReference()
    override var mViewInternalScopeRef: AtomicReference<Any> = AtomicReference()
    private var isPrepared = false

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        super.onCreateView(inflater, container, savedInstanceState)
        if (activity != null) {
            ResUtil.setCurrentPage(activity!!.javaClass.name, javaClass.simpleName)
        }
        initView(savedInstanceState)
        isPrepared = true
        return rootView
    }

    @Deprecated("Use initView(savedInstanceState: Bundle?)")
    override fun initView() {
    }

    protected abstract fun initView(savedInstanceState: Bundle?)

    @Suppress("DEPRECATION")
    fun vibrate(duration: Long) {
        context?.let {
            val vibrator = it.getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
            vibrator.vibrate(duration)
        }
    }

    private fun onVisibleChanged(isVisible: Boolean) {
        if (isVisible) onVisible() else onInVisible()
    }

    override fun onResume() {
        super.onResume()
        if (activity != null) {
            ResUtil.setCurrentPage(activity!!.javaClass.name, javaClass.simpleName)
        }
    }

    open fun onVisible() {}

    open fun onInVisible() {}

    override fun setUserVisibleHint(isVisibleToUser: Boolean) {
        super.setUserVisibleHint(isVisibleToUser)
        if (!isPrepared) return;
        onVisibleChanged(isVisibleToUser)
    }

    fun showToast(msg: String) = ToastUtil.showToast(msg)

    fun showToast(resId: Int, vararg params: String) =
        showToast(String.format(getString(resId, *params)))

    fun showToast(resId: Int) = showToast(getString(resId))

    override fun onDestroyView() {
        super.onDestroyView()
        removeViewLifecycleScope()
        progress.hideIfShowing()
    }
}

/**
 * BaseFragment with ViewModel
 */
abstract class BaseVMFragment<VM : BaseVM> : BaseFragment(), CommonUiEventObserver,
    InputFocusUiHolder by InputFocusUiHolder.Impl() {

    lateinit var viewModel: VM

    protected val isViewModelInitialized get() = ::viewModel.isInitialized

    protected abstract fun createViewModel(): VM

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        initViewModel()
        observeCommonUiEvent(viewModel)
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    private fun initViewModel() {
        viewModel = ViewModelProviders.of(this, ParamViewModelFactory(::createViewModel))
            .get(getVmClazz(this))
    }

    override fun showToast(message: Message, longToast: Boolean) {
        context ?: return
        if (longToast) {
            ToastUtil.showErrorToast(message.getContent(context!!))
        } else {
            ToastUtil.showToast(message.getContent(context!!))
        }
    }

    override fun speak(message: Message) {
        context ?: return
        speak(message.getContent(context!!))
    }

    override fun speakAndToast(message: Message) {
        context ?: return
        speakAndToast(message.getContent(context!!))
    }

    override fun showSnack(message: Message, type: SnackType) {
        context ?: return
        Snacker.show(activity, message.getContent(context!!), type)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        clearInputFocusBinding()
    }

}

/**
 * Base Dialog with ViewBinding
 */
abstract class BaseBindingFragment<VB : ViewBinding> : BaseFragment() {
    private var _binding: VB? = null
    val binding: VB? get() = _binding

    override fun getLayoutId(): Int = -1

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = inflateBindingWithGeneric(layoutInflater, container, false)
        rootView = _binding?.root
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}

/**
 * BaseFragment with ViewModel and ViewBinding
 */
abstract class BaseVMBindingFragment<VM : BaseVM, VB : ViewBinding> : BaseVMFragment<VM>() {
    private var _binding: VB? = null
    val binding: VB? get() = _binding

    override fun getLayoutId(): Int = -1

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = inflateBindingWithGeneric(layoutInflater, container, false)
        rootView = _binding?.root
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}