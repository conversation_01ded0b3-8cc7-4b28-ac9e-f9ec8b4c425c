package com.unis.reactivemvi.mvvm.kotlin.extensions

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.annotation.IdRes
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentTransaction
import com.unis.reactivemvi.fragmentlauncher.SupportFragment
import com.unis.reactivemvi.R
import com.unis.reactivemvi.mvvm.kotlin.DispatchResultFragment
import com.unis.reactivemvi.mvvm.kotlin.androidx.core_ktx.bundleOf
import java.io.Serializable

/**
 * <AUTHOR>
 * @date 2021/10
 * @desc Extension on FragmentActivity
 */


val FragmentActivity.intentExtras get() = intent.extras

interface UniversalActivityParam : Serializable {
    companion object {
        const val TAG = "universalActivityParam"
    }
}

val FragmentActivity.universalParam get() = intentExtras?.get(UniversalActivityParam.TAG)

/**
 * Extension on FragmentManager to operate transaction.
 * eg: In activity
 * ` supportFragmentManager.inTransaction{
 * `    remove(fragment1)
 * `    add(id,fragment2)
 * ` }
 */
inline fun FragmentManager.inTransaction(
    animate: Boolean = false,
    addToBackStack: Boolean = false,
    tag: String? = null,
    block: FragmentTransaction.(FragmentManager) -> FragmentTransaction
) {
    beginTransaction().apply {
        if (animate) setCustomAnimations(
            R.anim.entry_anim,
            R.anim.exit_anim,
            R.anim.pop_entry_anim,
            R.anim.pop_exit_anim
        )
        if (addToBackStack) addToBackStack(tag)
    }.block(this).commit()
}

fun FragmentActivity.addFragment(
        @IdRes containerId: Int,
        fragment: Fragment,
        tag: String? = null,
        addToBackStack: Boolean = false,
        animate: Boolean = false
) {
    val currentFragment = getCurrentFragment()
    supportFragmentManager.inTransaction(animate = animate, addToBackStack = addToBackStack, tag = tag ?: fragment.fragmentTag) {
        if (currentFragment != null) {
            hide(currentFragment)
        }
        add(containerId, fragment, tag ?: fragment.fragmentTag)
    }
}

fun FragmentActivity.replaceFragment(
        @IdRes containerId: Int,
        fragment: Fragment,
        tag: String? = null,
        addToBackStack: Boolean = false,
        animate: Boolean = false
) {
    supportFragmentManager.inTransaction(animate = animate, addToBackStack = addToBackStack, tag = tag ?: fragment.fragmentTag) {
        replace(containerId, fragment, tag ?: fragment.fragmentTag)
    }
}

private fun FragmentActivity.getCurrentFragment(): SupportFragment? {
    if (supportFragmentManager.backStackEntryCount > 0) {
        val fragmentTag = supportFragmentManager.getBackStackEntryAt(supportFragmentManager.backStackEntryCount - 1).name
        val topFragment = supportFragmentManager.findFragmentByTag(fragmentTag)
        if (topFragment is SupportFragment) {
            return topFragment
        }
    }
    return null
}

/**
 * StartActivityForResult with callback.
 * eg: `startActivityForResult<MainActivity>("param1" to "1","param2" to 2){ resultCode,data->
 *     ` if(resultCode == RESULT_OK){
 *     `    data?.let{}
 *     `}
 */
inline fun <reified AC : Activity> FragmentActivity.startActivityForResult(
    vararg extras: Pair<String, *>,
    noinline callback: (resultCode: Int, data: Intent?) -> Unit
) {
    val bundle = bundleOf(*extras)
    startActivityForResult<AC>(bundle, callback)
}

/**
 * StartActivityForResult with callback.
 * eg: `startActivityForResult<MainActivity>(bundle){ resultCode,data->
 *     ` if(resultCode == RESULT_OK){
 *     `    data?.let{}
 *     `}
 */
inline fun <reified AC : Activity> FragmentActivity.startActivityForResult(
    extras: Bundle? = null,
    noinline callback: (resultCode: Int, data: Intent?) -> Unit
) {
    val intent = Intent().setClass(this, AC::class.java)
    extras?.let { intent.putExtras(extras) }
    startActivityForResult(intent, callback)
}

/**
 * StartActivityForResult with callback.
 * eg: `startActivityForResult(intent){ resultCode,data->
 *     ` if(resultCode == RESULT_OK){
 *     `    data?.let{}
 *     `}
 */
fun FragmentActivity.startActivityForResult(
    intent: Intent,
    callback: (resultCode: Int, data: Intent?) -> Unit
) {
    DispatchResultFragment.getInstance(this).startActivityForResult(intent, callback)
}

/**
 * Find fragment by tag. [tag] is default to T::class.simpleName
 */
inline fun <reified T : Fragment> FragmentActivity.findFragmentByTag(
    tag: String = fragmentTagOf<T>()!!
) = supportFragmentManager.findFragmentByTag(tag) as? T