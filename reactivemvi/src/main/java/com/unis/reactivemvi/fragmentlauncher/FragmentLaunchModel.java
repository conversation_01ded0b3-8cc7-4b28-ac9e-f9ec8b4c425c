package com.unis.reactivemvi.fragmentlauncher;

import android.os.Bundle;

/**
 * Fragment launch model
 * For default model, start a fragment and it will be added to fragment container;
 *
 * For Single_top, start a fragment and it is in the top of fragment container,
 * and it will not be added to fragment container, show the top of fragment container directly,
 * call {@link SupportFragment#onNewIntent(Bundle)}, or added to fragment container;
 *
 * For Single_task, start a fragment and it is in fragment container, it will not be added to fragment container
 * and remove all of fragments on top of the fragment, call {@link SupportFragment#onNewIntent(Bundle)}, or added to fragment container;
 *
 * @Author: Dennis
 * @CreateDate: 2021/2/7 09:03
 */
public enum FragmentLaunchModel {

     DEFAULT, SINGLE_TOP, SINGLE_TASK
}
