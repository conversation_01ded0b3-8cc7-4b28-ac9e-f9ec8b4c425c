package com.unis.reactivemvi.fragmentlauncher;

import android.os.Bundle;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;

import com.unis.reactivemvi.R;

import java.util.List;

/**
 * @Author: Dennis
 * @CreateDate: 2021/2/7 09:02
 */
public class FragmentDelegate {

    private static final long SHUTTER_DURATION = 1000;

    private final FragmentActivity mContext;
    private final FragmentLifecycleObserver mFragmentLifecycleObserver;
    private FragmentManager mFragmentManager;
    private long mLastTime;
    private int mEnterAnim;
    private int mExitAnim;
    private int mPopEnterAnim;
    private int mPopExitAnim;

    public FragmentDelegate(FragmentActivity context) {
        mContext = context;
        mFragmentLifecycleObserver = new FragmentLifecycleObserver();
        mFragmentManager = context.getSupportFragmentManager();
        mFragmentManager.registerFragmentLifecycleCallbacks(mFragmentLifecycleObserver, false);
        overridePendingTransition(R.anim.entry_anim, R.anim.exit_anim, R.anim.pop_entry_anim, R.anim.pop_exit_anim);
    }

    public void setFragment(SupportFragment fragment) {
        setFragment(fragment, null);
    }

    /**
     * Add target fragment to the fragment container,and will be add to back stack
     * It is usually used to load fragment for the first time in {@link SupportFragmentActivity}
     * @param fragment target fragment
     * @param bundle supply the construction arguments for this fragment
     */
    public void setFragment(SupportFragment fragment, Bundle bundle) {
        if (fragment == null) {
            throw new NullPointerException("Fragment cannot be null");
        }
        if (isShutter()) return;
        if (bundle != null) {
            fragment.setArguments(bundle);
        }
        String tagName = fragment.getClass().getName();
        mFragmentManager.beginTransaction()
                .add(R.id.frame_layout_container, fragment, tagName)
                .addToBackStack(tagName)
                .commit();
    }

    /**
     * Add target fragment to the fragment container, and current fragment will be hided.
     * and will be add to back stack
     * @param currentFragment the current fragment
     * @param targetFragment target fragment
     * @param bundle supply the construction arguments for this fragment
     */
    private void addFragment(SupportFragment currentFragment, SupportFragment targetFragment, Bundle bundle) {
        if (targetFragment == null) {
            throw new NullPointerException("Fragment cannot be null");
        }
        if (isShutter()) return;
        if (bundle != null) {
            targetFragment.setArguments(bundle);
        }
        String tagName = targetFragment.getClass().getName();
        if (currentFragment == null) {
            mFragmentManager.beginTransaction()
                    .setCustomAnimations(mEnterAnim, mExitAnim, mPopEnterAnim, mPopExitAnim)
                    .add(R.id.frame_layout_container, targetFragment, tagName)
                    .addToBackStack(tagName)
                    .commit();
        } else {
            mFragmentManager.beginTransaction()
                    .setCustomAnimations(mEnterAnim, mExitAnim, mPopEnterAnim, mPopExitAnim)
                    .add(R.id.frame_layout_container, targetFragment, tagName)
                    .hide(currentFragment)
                    .addToBackStack(tagName)
                    .commit();
        }
    }

    public void addFragment(SupportFragment currentFragment, SupportFragment targetFragment, FragmentLaunchModel launchModel) {
        addFragment(currentFragment, targetFragment, null, launchModel);
    }

    public void addFragment(SupportFragment currentFragment, SupportFragment targetFragment, Bundle bundle, FragmentLaunchModel launchModel) {
        switch (launchModel) {
            case SINGLE_TOP:
                singleTopLaunchFragment(currentFragment, targetFragment, bundle);
                break;

            case SINGLE_TASK:
                singleTaskLaunchFragment(currentFragment, targetFragment, bundle);
                break;

            default:
                defaultLaunchFragment(currentFragment, targetFragment, bundle);
                break;
        }
    }

    /**
     * Launch fragment with {@link FragmentLaunchModel#DEFAULT}
     * @param currentFragment the current fragment
     * @param targetFragment target fragment
     * @param bundle supply the construction arguments for this fragment
     */
    private void defaultLaunchFragment(SupportFragment currentFragment, SupportFragment targetFragment, Bundle bundle) {
        addFragment(currentFragment, targetFragment, bundle);
    }

    /**
     * Launch fragment with {@link FragmentLaunchModel#SINGLE_TOP}
     * @param currentFragment the current fragment
     * @param targetFragment target fragment
     * @param bundle supply the construction arguments for this fragment
     */
    private void singleTopLaunchFragment(SupportFragment currentFragment, SupportFragment targetFragment, Bundle bundle) {
        List<SupportFragment> supportFragment = getSupportFragment();
        if (!supportFragment.isEmpty() &&
                supportFragment.get(supportFragment.size() -1).getClass().getName().equals(targetFragment.getClass().getName())) {
            SupportFragment fragment = supportFragment.get(supportFragment.size() - 1);
            fragment.onNewIntent(bundle != null? bundle : targetFragment.getArguments());
        } else {
            addFragment(currentFragment, targetFragment, bundle);
        }
    }

    /**
     * Launch fragment with {@link FragmentLaunchModel#SINGLE_TASK}
     * @param currentFragment the current fragment
     * @param targetFragment target fragment
     * @param bundle supply the construction arguments for this fragment
     */
    private void singleTaskLaunchFragment(SupportFragment currentFragment, SupportFragment targetFragment, Bundle bundle) {
        List<SupportFragment> supportFragment = getSupportFragment();
        if (!supportFragment.isEmpty()) {
            int index = -1;
            SupportFragment fragment = null;
            for (int i = supportFragment.size() - 1; i >= 0; i--) {
                if (supportFragment.get(i).getClass().getName().equals(targetFragment.getClass().getName())) {
                    index = i;
                    fragment = supportFragment.get(i);
                    break;
                }
            }
            if (index != -1) {
                fragment.onNewIntent(bundle != null? bundle : targetFragment.getArguments());
                for (int i = 0, times = supportFragment.size() -1 -index; i < times; i++) {
                    mFragmentManager.popBackStack();
                }
            } else {
                addFragment(currentFragment, targetFragment, bundle);
            }
        } else {
            addFragment(currentFragment, targetFragment, bundle);
        }
    }

    public List<SupportFragment> getSupportFragment() {
        return mFragmentLifecycleObserver.getFragmentList();
    }

    /**
     * Get current fragment
     * @return
     */
    public SupportFragment getCurrentVisibilityFragment() {
        Fragment currentFragment = mFragmentManager.findFragmentById(R.id.frame_layout_container);
        if (currentFragment instanceof SupportFragment) {
            return (SupportFragment) currentFragment;
        }
        return null;
    }

    /**
     * Get current fragment
     *
     * @return
     */
    public SupportFragment getCurrentVisibilityFragment(String tag) {
        Fragment currentFragment = mFragmentManager.findFragmentByTag(tag);
        if (currentFragment instanceof SupportFragment) {
            return (SupportFragment) currentFragment;
        }
        return null;
    }

    /**
     * finish the current fragment
     */
    public void finishFragment() {
        if (mFragmentManager.getBackStackEntryCount() > 1) {
            mFragmentManager.popBackStack();
        } else {
            mContext.finish();
        }
    }

    /**
     * finish the current fragment, and transmit data to previous fragment
     */
    public void finishFragmentForResult(Bundle data) {
        if (mFragmentManager.getBackStackEntryCount() > 1) {
            mFragmentManager.popBackStack();
            List<SupportFragment> supportFragment = getSupportFragment();
            if (supportFragment != null && supportFragment.size() > 1) {
                SupportFragment previousFragment = supportFragment.get(supportFragment.size() - 2);
                previousFragment.onFragmentForResult(data);
            }
        } else {
            mContext.finish();
        }
    }

    /**
     * Determine whether the current fragment is the root of the activity
     * @return true
     */
    public boolean isRootCurrentFragment() {
        return mFragmentManager.getBackStackEntryCount() == 1;
    }

    public void overridePendingTransition(int enterAnim, int exitAnim, int popEnterAnim, int popExitAnim) {
        mEnterAnim = enterAnim;
        mExitAnim = exitAnim;
        mPopEnterAnim = popEnterAnim;
        mPopExitAnim = popExitAnim;
    }

    /**
     * Judge whether to start a fragment quickly
     * @return true is a quick start
     */
    private boolean isShutter() {
        if (System.currentTimeMillis() - mLastTime < SHUTTER_DURATION) {
            mLastTime = System.currentTimeMillis();
            return true;
        }
        return false;
    }

    public void cancelDelegate() {
        if (mFragmentLifecycleObserver != null) {
            mFragmentManager.unregisterFragmentLifecycleCallbacks(mFragmentLifecycleObserver);
        }
    }

    public boolean onBackPressed() {
        SupportFragment currentVisibilityFragment = getCurrentVisibilityFragment();
        if (currentVisibilityFragment != null) {
            return currentVisibilityFragment.onBackPressed();
        }
        return false;
    }
}
