package com.unis.reactivemvi.fragmentlauncher;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;

import com.customer.widget.core.LincBaseActivity;
import com.google.android.material.appbar.AppBarLayout;
import com.unis.reactivemvi.R;

/**
 * @Author: Dennis
 * @CreateDate: 2021/2/7 10:10
 */
public abstract class SupportFragmentActivity extends LincBaseActivity {

    private AppBarLayout mAppBarLayout;
    private Toolbar mToolbar;
    private FragmentDelegate mFragmentDelegate;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(getLayoutId());
        mAppBarLayout = (AppBarLayout)findViewById(R.id.toolbar_layout);
        mToolbar = (Toolbar)findViewById(R.id.toolbar);
        mAppBarLayout.setVisibility(isShowToolbar() ? View.VISIBLE : View.GONE);
        mFragmentDelegate = new FragmentDelegate(this);
        mFragmentDelegate.setFragment(getRootFragment(getIntent()));
    }


    protected int getLayoutId(){
        return R.layout.activity_support_fragment;
    }


    /**
     * Root fragment in fragment container
     * @return {@link SupportFragment}
     */
    protected abstract SupportFragment getRootFragment(Intent intent);

    public FragmentDelegate getFragmentDelegate() {
        return mFragmentDelegate;
    }

    protected boolean isFragmentVisible(String fragmentTag) {
        String currentFragmentTag = "";
        SupportFragment fragment = getFragmentDelegate().getCurrentVisibilityFragment();
        if (fragment != null) {
            currentFragmentTag = fragment.getTag();
        }
        return TextUtils.equals(fragmentTag, currentFragmentTag);
    }

    /**
     * If not need a toolbar, override it and return false.
     * @return true is show toolbar, or hide toolbar
     */
    protected boolean isShowToolbar() {
        return true;
    }

    protected void setToolbarVisibility(int visibility) {
        mAppBarLayout.setVisibility(visibility);
    }

    protected Toolbar getToolbar() {
        return mToolbar;
    }

    @Override
    protected void onDestroy() {
        if (mFragmentDelegate != null) {
            mFragmentDelegate.cancelDelegate();
        }
        super.onDestroy();
    }

    @Override
    public void onBackPressed() {
        if (mFragmentDelegate.onBackPressed()) {
            return;
        }
        if (mFragmentDelegate.isRootCurrentFragment()) {
            finish();
            return;
        }
        super.onBackPressed();
    }
}
