package com.unis.reactivemvi.fragmentlauncher;

import android.os.Bundle;

import androidx.fragment.app.FragmentActivity;

import com.customer.widget.core.LincBaseFragment;

import java.util.List;

/**
 * @Author: Dennis
 * @CreateDate: 2021/2/7 10:36
 */
public abstract class SupportFragment extends LincBaseFragment implements IOnBackPressedListener{

    /**
     * call when start fragment with {@link FragmentLaunchModel#SINGLE_TOP}, and the fragment in the top of fragment container
     * or when start fragment with {@link FragmentLaunchModel#SINGLE_TASK}, and the fragment in fragment container
     * @param bundle
     */
    protected void onNewIntent(Bundle bundle){}

    /**
     * start a fragment, and launch model with {@link FragmentLaunchModel#DEFAULT}
     * @param fragment target fragment
     */
    public void startFragment(SupportFragment fragment) {
        startFragment(fragment, FragmentLaunchModel.DEFAULT);
    }

    /**
     * start a fragment
     * @param fragment target fragment
     * @param launchModel launch model for starting a fragment
     * @see FragmentLaunchModel
     */
    public void startFragment(SupportFragment fragment, FragmentLaunchModel launchModel) {
        startFragment(fragment, null, launchModel);
    }

    /**
     * start a fragment, and launch model with {@link FragmentLaunchModel#DEFAULT}
     * @param fragment target fragment
     * @param bundle supply the construction arguments for this fragment
     */
    public void startFragment(SupportFragment fragment, Bundle bundle) {
        FragmentDelegate fragmentDelegate = getAttachActivity().getFragmentDelegate();
        fragmentDelegate.addFragment(this, fragment, bundle, FragmentLaunchModel.DEFAULT);
    }

    /**
     * start a fragment
     * @param fragment target fragment
     * @param bundle supply the construction arguments for this fragment
     * @param launchModel launch model for starting a fragment
     * @see FragmentLaunchModel
     */
    public void startFragment(SupportFragment fragment, Bundle bundle, FragmentLaunchModel launchModel) {
        FragmentDelegate fragmentDelegate = getAttachActivity().getFragmentDelegate();
        fragmentDelegate.addFragment(this, fragment, bundle, launchModel);
    }

    /**
     * finish the current fragment
     */
    public void finishFragment() {
        FragmentDelegate fragmentDelegate = getAttachActivity().getFragmentDelegate();
        fragmentDelegate.finishFragment();
    }

    /**
     * finish the current fragment, and transmit data to previous fragment
     */
    public void finishFragmentForResult(Bundle data) {
        FragmentDelegate fragmentDelegate = getAttachActivity().getFragmentDelegate();
        fragmentDelegate.finishFragmentForResult(data);
    }

    /**
     * Get activity that fragment attach
     *
     * @return {@link SupportFragmentActivity}
     */
    protected SupportFragmentActivity getAttachActivity() {
        FragmentActivity activity = getActivity();
        if (activity instanceof SupportFragmentActivity) {
            return (SupportFragmentActivity) activity;
        } else {
            if (activity != null) {
                throw new ClassCastException(activity.getClass().getName() + "cannot be cast " + SupportFragmentActivity.class.getName());
            }
            throw new NullPointerException("activity is null");
        }
    }

    /**
     * @deprecated only for debugging to use
     * @return
     */
    @Deprecated
    public List<SupportFragment> getSupportFragment() {
        return getAttachActivity().getFragmentDelegate().getSupportFragment();
    }

    /**
     * For receive data form the last fragment.
     * When fragment use {@link FragmentDelegate#finishFragmentForResult(Bundle)} to finish, it will be called
     * @param data
     */
    protected void onFragmentForResult(Bundle data) {
    }

    public void overridePendingTransition(int enterAnim, int exitAnim, int popEnterAnim, int popExitAnim) {
        getAttachActivity().getFragmentDelegate().overridePendingTransition(enterAnim, exitAnim, popEnterAnim, popExitAnim);
    }

    /**
     * @return Return <code>true</code> to prevent this event from being propagated
     * further, or <code>false</code> to indicate that you have not handled
     * this event and it should continue to be propagated.
     */
    @Override
    public boolean onBackPressed() {
        return false;
    }
}
