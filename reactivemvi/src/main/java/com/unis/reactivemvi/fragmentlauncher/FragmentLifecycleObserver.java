package com.unis.reactivemvi.fragmentlauncher;

import android.content.Context;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;

import java.util.ArrayList;
import java.util.List;

/**
 * Observe the SupportFragment in the current activity
 * @see SupportFragment
 * @Author: Dennis
 * @CreateDate: 2021/2/7 11:02
 */
public class FragmentLifecycleObserver extends FragmentManager.FragmentLifecycleCallbacks {

    private final List<SupportFragment> mFragmentList = new ArrayList<>();

    @Override
    public void onFragmentAttached(FragmentManager fm, Fragment f, Context context) {
        super.onFragmentAttached(fm, f, context);
        if (f instanceof SupportFragment) {
            mFragmentList.add((SupportFragment) f);
        }
    }

    @Override
    public void onFragmentDetached(FragmentManager fm, Fragment f) {
        super.onFragmentDetached(fm, f);
        if (f instanceof SupportFragment) {
            mFragmentList.remove((SupportFragment) f);
        }
    }

    /**
     * Get all of SupportFragment in the current activity
     * @return
     */
    public List<SupportFragment> getFragmentList() {
        return mFragmentList;
    }
}
