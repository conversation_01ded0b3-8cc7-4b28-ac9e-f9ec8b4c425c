package com.unis.reactivemvi.fragmentlauncher;

/**
 * Called when the activity has detected the user's press of the back
 * key.  The default implementation simply finishes the current activity,
 * but you can override this to do whatever you want.
 * @return Return <code>true</code> to prevent this event from being propagated
 * further, or <code>false</code> to indicate that you have not handled
 * this event and it should continue to be propagated.
 *
 * @Author: Dennis
 * @CreateDate: 2021/2/7 11:02
 */
public interface IOnBackPressedListener {

    boolean onBackPressed();
}
