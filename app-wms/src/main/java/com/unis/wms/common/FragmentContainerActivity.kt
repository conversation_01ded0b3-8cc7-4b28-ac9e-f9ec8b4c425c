package com.unis.wms.common

import android.os.Bundle
import android.view.View
import com.customer.widget.TimeElapsedCallBack
import com.customer.widget.extensions.setVisible
import com.unis.reactivemvi.fragmentlauncher.SupportFragment
import com.unis.reactivemvi.SnackType
import com.unis.reactivemvi.mvi.ReactiveActivity
import com.unis.reactivemvi.mvi.ReactiveUiState
import com.unis.reactivemvi.mvi.ReactiveViewModel
import com.unis.reactivemvi.mvvm.kotlin.BaseFragment
import com.unis.reactivemvi.mvvm.kotlin.Message
import com.unis.reactivemvi.mvvm.kotlin.extensions.addFragment
import com.unis.reactivemvi.mvvm.kotlin.extensions.replaceFragment
import com.unis.wms.R
import com.unis.wms.common.taskworktime.TaskWorkTimeConfig
import com.unis.wms.common.taskworktime.TaskWorkTimeViewModel
import com.unis.wms.databinding.ActivityFragmentContainerBinding

abstract class FragmentContainerActivity<VM : ReactiveViewModel<*, U>, U : ReactiveUiState>
    : ReactiveActivity<VM, U, ActivityFragmentContainerBinding>() {

    private val taskWorkTimeViewModel by lazy { TaskWorkTimeViewModel() }

    private val containerId get() = binding.frameLayoutContainer.id

    override fun initView(savedInstanceState: Bundle?) {
        binding.apply {
            if (isShowToolbar()) {
                toolbarLayout.setVisible()
            }
        }

        showWorkFragment()
        addBackPressedCallback(this@FragmentContainerActivity::handlerBackPressed)
    }

    private fun showWorkFragment() {
        addFragment(getInitFragment())
    }

    fun addFragment(fragment: BaseFragment, animate : Boolean = false) {
        addFragment(containerId = containerId, fragment = fragment, tag = fragment::class.java.simpleName, addToBackStack = true, animate = animate)
    }

    fun replaceFragment(fragment: BaseFragment) {
        replaceFragment(containerId = containerId, fragment = fragment, tag = fragment::class.java.simpleName, addToBackStack = true)
    }

    protected abstract fun getInitFragment(): BaseFragment

    protected open fun isShowToolbar(): Boolean = false

    fun changeToolbarTitle(title: String) {
        binding.apply {
           toolbar.title = title
        }
    }

    open fun handlerBackPressed(): Boolean {
        val isHandled = handlerChildFragmentBackPressed()
        if (isHandled) {
            return true
        }
        return onFinishFragment()
    }

    private fun handlerChildFragmentBackPressed(): Boolean {
        val currentVisibilityFragment = getCurrentVisibilityFragment()
        if (currentVisibilityFragment != null) {
            return currentVisibilityFragment.onBackPressed()
        }
        return false
    }

    private fun getCurrentVisibilityFragment(): SupportFragment? {
        if (supportFragmentManager.backStackEntryCount > 0) {
            val fragmentTag = supportFragmentManager.getBackStackEntryAt(supportFragmentManager.backStackEntryCount - 1).name
            val topFragment = supportFragmentManager.findFragmentByTag(fragmentTag)
            if (topFragment is SupportFragment) {
                return topFragment
            }
        }
        return null
    }

    fun onFinishFragment(): Boolean {
        val backStackEntryCount = supportFragmentManager.backStackEntryCount
        if (backStackEntryCount > 1) {
            supportFragmentManager.popBackStack()
            return true
        }
        finish()
        return false
    }

    fun startTaskWorkTime(config: TaskWorkTimeConfig) {
        if (!enableManualTimer()) {
            return
        }
        taskWorkTimeViewModel.initStartTime(config) { duration->
            if(duration > 0 ){
                showSnack(Message.ResourceMessage(R.string.resume_timer_to_continue_task), SnackType.ErrorV2(title = R.string.text_tip))
            }
            startTimeView(duration)
        }
    }

    private fun startTimeView(workingDuration: Long) {
        binding.apply {
            timeElapsedView.visibility = View.VISIBLE
            timeElapsedView.startTime(
                duration = workingDuration,
                callBack = object : TimeElapsedCallBack {
                    override fun onTimePause() {
                        taskWorkTimeViewModel.pauseWorkTime {
                            pauseTimeView()
                            showSnack(Message.ResourceMessage(R.string.resume_timer_to_continue_task), SnackType.ErrorV2(title = R.string.text_tip))
                        }
                    }

                    override fun onTimeResume() {
                        taskWorkTimeViewModel.resumeWorkTime {
                            resumeTimeView()
                        }
                    }
                }
            )
        }
    }

    private fun resumeTimeView() {
        binding.apply {
            timeElapsedView.resumeTime()
        }
    }
    
    private fun pauseTimeView() {
        binding.apply {
            timeElapsedView.pauseTime()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (enableManualTimer()) {
            taskWorkTimeViewModel.pauseWorkTime {  }
        }
    }

}