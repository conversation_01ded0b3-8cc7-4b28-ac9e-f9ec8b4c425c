apply plugin: 'com.android.application'
apply from: file("${rootDir}/config/quality.gradle")
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'

static def gitVersionCode() {
    def previousVersion = System.getProperty("lastversion")
    if (previousVersion == null) {
        def cmd = 'git rev-list HEAD --count'
        int versionCode = 1
        try{
            versionCode = Integer.parseInt(cmd.execute().text.trim());
        } catch(NumberFormatException ex){
            System.out.println("not a number" + ex);
        }
        return versionCode
    } else {
        return previousVersion.toInteger() + 1
    }
}

static def gitVersionName() {
    String[] tagList = getTagList()
    def date = new Date().format('yyyyMMdd').toString()
    def envTag = System.getProperty("env") == null ? 'V2' : System.getProperty("env")
    //def buildTime = tagList.findAll { it.contains(envTag + '.' + date) }.size() + 1
    def version = envTag + '.' + date + '(' + gitVersionCode()+ ')'
    return version
}

private static String[] getTagList() {
    def cmd = 'git pull --tags'
    cmd.execute().text.trim()
    cmd = 'git tag'
    def tags = cmd.execute().text.trim()
    def tagList = tags.split("\n")
    tagList
}

private static def isRequireCycleCountModule() {
    def modules = System.getProperty("modules")
    if (modules != null) {
        return modules.split("-").contains("cyclecount")
    }
    return false
}

android {
    namespace "com.unis.wms"
    compileSdkVersion rootProject.ext.android["compileSdkVersion"]
    buildToolsVersion rootProject.ext.android["buildToolsVersion"]

    defaultConfig {
        applicationId "com.unis.wms"
        minSdkVersion rootProject.ext.android["minSdkVersion"]
        targetSdkVersion rootProject.ext.android["targetSdkVersion"]
        versionCode gitVersionCode()
        versionName gitVersionName()
        multiDexEnabled true
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        ndk {
            abiFilters 'armeabi-v7a', 'arm64-v8a', 'x86_64'
        }
    }

    signingConfigs {
        debug {
            storeFile file("./app-wms-debug.jks")
            storePassword "wmsdebug"
            keyAlias "wmsdebug"
            keyPassword "wmsdebug"
        }
        stage {
            storeFile file("./app-wms-stage.jks")
            storePassword "wmsstage"
            keyAlias "wmsstage"
            keyPassword "wmsstage"
        }
        preview {
            storeFile file("./app-wms-preview.jks")
            storePassword "wmspreview"
            keyAlias "wmspreview"
            keyPassword "wmspreview"
        }
        release {
            storeFile file("./app-wms-release.jks")
            storePassword "wmsrelease"
            keyAlias "wmsrelease"
            keyPassword "wmsrelease"
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
            def env = System.getProperty('env')
            switch (env) {
                case 'PROD':
                    buildConfigField("String", "DEFAULT_MAIN_SERVER_URL", "\"https://unis.item.com/api/\"")
                    buildConfigField("String", "VERSION_NAME", "\"${gitVersionName()}\"")
                    buildConfigField("String", "DEFAULT_INFO_CLOCK_SERVER_URL", "\"https://clockapi.logisticsteam.com:9090/api/\"")
                    break
                default:
                    buildConfigField("String", "DEFAULT_MAIN_SERVER_URL", "\"https://unis.item.com/api/\"")
                    buildConfigField("String", "VERSION_NAME", "\"${gitVersionName()}\"")
                    buildConfigField("String", "DEFAULT_INFO_CLOCK_SERVER_URL", "\"https://clockapi.logisticsteam.com:9090/api/\"")
                    break
            }
            buildConfigField("String", "DATA_COLLECTION_SERVER_URL", "\"https://data-collection.item.com/\"")
            ndk {
                //noinspection ChromeOsAbiSupport
                abiFilters 'armeabi-v7a', 'arm64-v8a'
            }
        }

        debug {
            applicationIdSuffix ".debug"
            signingConfig signingConfigs.debug
            buildConfigField("String", "DEFAULT_MAIN_SERVER_URL", "\"https://wms-dev.item.pub/api/\"")
            buildConfigField("String", "DEFAULT_INFO_CLOCK_SERVER_URL", "\"https://clockapi.logisticsteam.com:9088/api/\"")
            buildConfigField("String", "DATA_COLLECTION_SERVER_URL", "\"https://stage.logisticsteam.com/data-collection/\"")
            debuggable true
            jniDebuggable true
            ndk {
                abiFilters 'armeabi-v7a', 'arm64-v8a', 'x86_64'
            }
        }

        stage {
            applicationIdSuffix ".stage"
            signingConfig signingConfigs.stage
            buildConfigField("String", "DEFAULT_MAIN_SERVER_URL", "\"https://wms-staging.item.com/api/\"")
            buildConfigField("String", "DATA_COLLECTION_SERVER_URL", "\"https://stage.logisticsteam.com/data-collection/\"")
            buildConfigField("String", "DEFAULT_INFO_CLOCK_SERVER_URL", "\"https://clockapi.logisticsteam.com:9088/api/\"")
            matchingFallbacks = ['debug']
            ndk {
                //noinspection ChromeOsAbiSupport
                abiFilters 'armeabi-v7a', 'arm64-v8a'
            }
        }

        preview {
            applicationIdSuffix ".preview"
            signingConfig signingConfigs.preview
            buildConfigField("String", "DEFAULT_MAIN_SERVER_URL", "\"https://wms-preview.item.com/api/\"")
            buildConfigField("String", "DEFAULT_INFO_CLOCK_SERVER_URL", "\"https://clockapi.logisticsteam.com:9088/api/\"")
            buildConfigField("String", "DATA_COLLECTION_SERVER_URL", "\"https://stage.logisticsteam.com/data-collection/\"")
            matchingFallbacks = ['debug']
            ndk {
                //noinspection ChromeOsAbiSupport
                abiFilters 'armeabi-v7a', 'arm64-v8a'
            }
        }
    }

    variantFilter {
        for (taskName in project.gradle.startParameter.taskNames) {
            def buildTypeName = it.buildType.name
            def taskNameUpper = taskName.toUpperCase()
            def buildTypeNameUpper = buildTypeName.toUpperCase()
            if (taskNameUpper.contains("ASSEMBLE") && !taskNameUpper.endsWith(buildTypeNameUpper)) {
                it.setIgnore(true)
                println "ignore build type:" + it.buildType.name
            }
        }
    }

    applicationVariants.all { variant ->
        variant.outputs.each { output ->
            output.versionCodeOverride = gitVersionCode()
            output.versionNameOverride = gitVersionName()
            def file = new File("$projectDir/version")
            file.createNewFile()
            file.text = """{"versionName":"$versionName","versionCode":"$versionCode","name":"wms-app.apk","message":"update issue"}\n"""
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    lintOptions {
        disable 'InvalidPackage'
        abortOnError false
    }
    packagingOptions {
        exclude 'META-INF/services/javax.annotation.processing.Processor'
    }
    buildFeatures {
        buildConfig true
        viewBinding true
    }
    kotlinOptions {
        // Ignore @OptIn warnings, Force 'SAM conversions = class' to compatible with AspectJ
        freeCompilerArgs = ["-opt-in=kotlin.RequiresOptIn", "-Xsam-conversions=class"]
        // Enable definitely non-nullable types
        languageVersion = "1.7"
    }
}

dependencies {

    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation files('lib/ZSDK_ANDROID_API.jar')
    testImplementation rootProject.ext.dependencies["junit"]
    androidTestImplementation rootProject.ext.dependencies["test-runner"]
    androidTestImplementation rootProject.ext.dependencies["test-rules"]

    implementation files('src/main/jniLibs/Msc.jar')
    implementation files('src/main/jniLibs/Sunflower.jar')
    implementation files('src/main/jniLibs/API3_LIB-release.aar')
    if (rootProject.ext.activeCycleCountModule || isRequireCycleCountModule()) {
        implementation project(':cyclecount')
    }
    implementation project(':reactivemvi')
    implementation project(':date-time-picker')
    implementation rootProject.ext.dependencies["ahbottomnavigation"]
    implementation rootProject.ext.dependencies["firebase-core"]
    implementation rootProject.ext.dependencies["firebase-storage"]
    implementation rootProject.ext.dependencies["firebase-database"]
    implementation rootProject.ext.dependencies["firebase-messaging"]
    implementation rootProject.ext.dependencies["firebase-config"]
    implementation rootProject.ext.dependencies["imagepipeline-okhttp3"]
    implementation rootProject.ext.dependencies["advrecyclerview"]
    implementation rootProject.ext.dependencies["magicprogresswidget"]
    implementation rootProject.ext.dependencies["io-socket"]
}

configurations.configureEach {
    resolutionStrategy.eachDependency { DependencyResolveDetails details ->
        def requested = details.requested
        if (requested.group == 'com.android.support') {
            // Skip multidex because it follows a different versioning pattern.
            if (!requested.name.startsWith("multidex")) {
                details.useVersion rootProject.ext.support_version
            }
        }
    }
}

apply plugin: 'com.google.gms.google-services'