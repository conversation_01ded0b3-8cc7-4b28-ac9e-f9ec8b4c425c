apply from: file("${rootDir}/config/library.gradle")
apply from: file("${rootDir}/config/quality.gradle")
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'

android {
    namespace "com.linc.platform"
    lintOptions {
        lintConfig file("lint.xml")
        abortOnError false
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = "1.8"
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    testImplementation rootProject.ext.dependencies["junit"]

    api rootProject.ext.dependencies["appcompat"]
    api rootProject.ext.dependencies["lifecycle"]
    api rootProject.ext.dependencies["rxjava"]
    api rootProject.ext.dependencies["support-annotations"]
    api rootProject.ext.dependencies["retrofit"]
    implementation rootProject.ext.dependencies["converter-gson"]
    api rootProject.ext.dependencies["adapter-rxjava"]
    api rootProject.ext.dependencies["rxandroid"]
    api rootProject.ext.dependencies["logger"]
    api rootProject.ext.dependencies["gson"]
    api rootProject.ext.dependencies["annotation-api"]
    implementation rootProject.ext.dependencies["okhttp3"]
    api rootProject.ext.dependencies["stream"]
    api rootProject.ext.dependencies["litepal"]
    api rootProject.ext.dependencies["stetho"]
    api rootProject.ext.dependencies["stetho-okhttp3"]
    api rootProject.ext.dependencies["EventBus"]
    api rootProject.ext.dependencies["play-services-maps"]
    api rootProject.ext.dependencies["play-services-location"]
    api rootProject.ext.dependencies["play-services-analytics"]
    api rootProject.ext.dependencies["play-services-places"]
    api rootProject.ext.dependencies["play-services-gcm"]
    api rootProject.ext.dependencies["play-services-base"]
    api rootProject.ext.dependencies["google-maps-utils"]
    api rootProject.ext.dependencies["BaseRecyclerViewAdapterHelper"]
    api rootProject.ext.dependencies["buggly"]
    api rootProject.ext.dependencies["kotlin-stdlib"]
    api rootProject.ext.dependencies["kotlin-reflect"]
    api rootProject.ext.dependencies["coroutines-core"]
    api rootProject.ext.dependencies["coroutines-android"]
    api rootProject.ext.dependencies["work-runtime"]
    api rootProject.ext.dependencies["room"]
    kapt rootProject.ext.dependencies["room-kapt"]
    api rootProject.ext.dependencies["room-ktx"]
    api project(':app-res')

    api rootProject.ext.dependencies["soLoader"]
    api(rootProject.ext.dependencies["flipper"], {
        exclude group: 'com.squareup.okhttp3'
        exclude group: 'org.jetbrains.kotlin'
        exclude group: 'org.jetbrains.kotlinx'
        exclude group: 'androidx.core'
        exclude group: 'androidx.appcompat'
        exclude group: 'androidx.annotation'
        exclude group: 'androidx.sqlite'
        exclude group: 'com.squareup'
    })
    api(rootProject.ext.dependencies["flipper-network-plugin"], {
        exclude group: 'com.squareup.okhttp3'
        exclude group: 'org.jetbrains.kotlin'
        exclude group: 'org.jetbrains.kotlinx'
        exclude group: 'androidx.core'
        exclude group: 'androidx.appcompat'
        exclude group: 'androidx.annotation'
        exclude group: 'androidx.sqlite'
        exclude group: 'com.squareup'
    })
}