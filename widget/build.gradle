apply from: file("${rootDir}/config/library.gradle")
apply plugin: 'kotlin-android'

android {
    namespace "com.customer.widget"

    lintOptions {
        disable 'InvalidPackage'
        abortOnError false
    }

    buildFeatures {
        viewBinding true
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = "1.8"
    }
}


dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'androidx.appcompat:appcompat:1.0.0'
    androidTestImplementation('androidx.test.espresso:espresso-core:3.1.0', {
        exclude group: 'com.android.support', module: 'support-annotations'
    })
    testImplementation rootProject.ext.dependencies["junit"]

    implementation project(':platform')
    api rootProject.ext.dependencies["design"]
    api rootProject.ext.dependencies["recyclerview"]
    api rootProject.ext.dependencies["cardview"]
    api rootProject.ext.dependencies["PhotoView"]
    api rootProject.ext.dependencies["legacy-support-v4"]
    api rootProject.ext.dependencies["fresco"]
    implementation rootProject.ext.dependencies["searchview"]
    api rootProject.ext.dependencies["constraint-layout"]
    implementation rootProject.ext.dependencies["circleindicator"]
    api rootProject.ext.dependencies["zxing"]
    api rootProject.ext.dependencies["RxBinding"]
    implementation rootProject.ext.dependencies["SubsamplingScaleImageView"]
    implementation rootProject.ext.dependencies["PhotoDraweeView"]
    api rootProject.ext.dependencies["qmui-android"]
    api rootProject.ext.dependencies["flexbox"]
    api rootProject.ext.dependencies["ios-switch"]
    api rootProject.ext.dependencies["GSYVideoPlayer"]
    api rootProject.ext.dependencies["GroupedRecyclerViewAdapter"]
}
