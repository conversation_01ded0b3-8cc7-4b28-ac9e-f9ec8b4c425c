<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.customer.widget">

    <application
        android:allowBackup="true"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:theme="@style/appThemeStyle">
        <activity
            android:name=".photoview.PhotoViewV1Activity"
            android:exported="true" />
        <activity
            android:name=".photoview.PhotoViewActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden" />
        <activity
            android:name=".squarecamera.CameraActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden" />
        <activity
            android:name=".photolayout.photozoom.PhotoZoomActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden" />
        <activity
            android:name=".squarecamera.RuntimePermissionActivity"
            android:theme="@style/squarecamera__CameraFullScreenTheme.NoUI" />
    </application>

</manifest>