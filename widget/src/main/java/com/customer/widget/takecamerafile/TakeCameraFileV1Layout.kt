package com.customer.widget.takecamerafile

import android.content.Context
import android.graphics.Bitmap
import androidx.constraintlayout.widget.ConstraintLayout
import com.google.android.material.tabs.TabLayout
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.widget.AppCompatButton
import androidx.appcompat.widget.AppCompatTextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.text.TextUtils
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import com.customer.widget.R
import com.customer.widget.scanner.core.CameraUtils
import com.linc.platform.utils.TimeUtil
import com.linc.platform.utils.doubleClick
import com.data_collection.DCActionConstants
import com.data_collection.models.ActionType
import com.data_collection.models.SendDataModel
import com.customer.widget.data_collection.DCAppCompatImageButton
import com.customer.widget.data_collection.DCAppCompatImageView
import com.customer.widget.data_collection.DCAppCompatTextView
import com.customer.widget.data_collection.DCTabLayout
import org.greenrobot.eventbus.EventBus


/**
 * @Description
 * <AUTHOR>
 * @Date 2022/4/6
 */
class TakeCameraFileV1Layout @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr), View.OnClickListener, OnTakeCameraCallBack {
    private lateinit var cameraPreview: CameraPreview
    private var rootView: ViewGroup
    private val btnTakePicture by lazy { findViewById<DCAppCompatImageButton>(R.id.take_picture_or_video_btn) }
    private val btnRetake by lazy { findViewById<DCAppCompatTextView>(R.id.retake_btn) }
    private val confirmPhotoLayout by lazy { findViewById<LinearLayout>(R.id.confirm_photo_layout) }
    private val btnReUse by lazy { findViewById<DCAppCompatTextView>(R.id.use_photo_video_btn) }
    private val btnCancel by lazy { findViewById<DCAppCompatImageView>(R.id.cancel_camera_btn) }
    private val btnFlash by lazy { findViewById<DCAppCompatImageView>(R.id.switch_flash_btn) }
    private val takePhotoTitleTv by lazy { findViewById<AppCompatTextView>(R.id.tv_take_photo_title) }
    private val takeVideoTimeLayout by lazy { findViewById<LinearLayout>(R.id.take_video_time_layout) }
    private val takePhotoTimeTv by lazy { findViewById<AppCompatTextView>(R.id.tv_take_video_time) }
    private val takePhotoMaxTimeTv by lazy { findViewById<AppCompatTextView>(R.id.tv_take_video_limit_time) }
    private val recyclerView by lazy { findViewById<RecyclerView>(R.id.recycler_view) }
    private val photoTagAdapter by lazy { FileTagAdapter() }
    private val tabTakePhoto by lazy { findViewById<DCTabLayout>(R.id.take_photo_video_tab_layout) }
    private val previewView by lazy { findViewById<ViewGroup>(R.id.preview_layout) }

    private var onTakeFileResultCallBacks: List<OnTakeFileResultCallBack> = listOf()
    private var fileResultList: List<TakeFileResult> = listOf()
    private var takeFileMode: TakeFileMode? = null
    private var flashState: Boolean = false
    private var iTakeCamera: ITakeCamera? = null
    private var fileType: TakeCameraFileType = TakeCameraFileType.PHOTO
    private var needTakeVideo: Boolean = false

    init {
        val rootLp = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT)
        rootView = LayoutInflater.from(context).inflate(R.layout.layout_take_photo_v1, null) as ViewGroup
        addView(rootView, rootLp)
        updateCameraPreview(true)
        btnTakePicture.setOnClickListener(this)
        btnRetake.setOnClickListener(this)
        btnReUse.setOnClickListener(this)
        btnCancel.setOnClickListener(this)
        btnFlash.setOnClickListener(this)
        recyclerView.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            adapter = photoTagAdapter
            photoTagAdapter.setOnItemClickListener { adapter, _, position ->
                val photoTag = adapter.getItem(position) as FileTag
                photoTag.apply {
                    photoTagAdapter.data.forEach { item ->
                        item.isSelected = item.tag == this.tag
                    }
                    updateTakeCameraTitle(tag)
                }
                photoTagAdapter.notifyDataSetChanged()
                EventBus.getDefault().post(
                    SendDataModel(
                        actionType = ActionType.CLICK,
                        value = photoTag.tag,
                        actionKey = DCActionConstants.TAKE_PHOTO_SELECT_TAG))
            }
        }
        tabTakePhoto.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(p0: TabLayout.Tab?) {
                setTakeCameraType(TakeCameraFileType.values()[p0?.position ?: 0])
            }

            override fun onTabUnselected(p0: TabLayout.Tab?) {
            }

            override fun onTabReselected(p0: TabLayout.Tab?) {
            }
        })
        setTakeCameraType(TakeCameraFileType.PHOTO)
        cameraPreview.defaultCameraId = CameraUtils.getDefaultCameraId()
    }

    private fun updateCameraPreview(showCameraPreview: Boolean) {
        if (showCameraPreview) {
            val rootLp = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT)
            cameraPreview = CameraPreview(context)
            rootView.addView(cameraPreview, 0, rootLp)
        } else {
            rootView.removeView(cameraPreview)
        }

    }

    private fun setTakeCameraType(type: TakeCameraFileType) {
        fileType = type
        iTakeCamera = when (type) {
            TakeCameraFileType.PHOTO -> {
                TakeCameraFileWithPhoto(this)
            }

            TakeCameraFileType.VIDEO -> {
                TakeCameraFileWithVideo(videoParam(), this)
            }
        }
        updateTakeVideoView(false)
        updateTakeCameraTitle(currentPhotoWithTag())
    }

    private fun updateTakeVideoView(showPreview: Boolean) {
        takeVideoTimeLayout.visibility = if (fileType == TakeCameraFileType.VIDEO && !showPreview) View.VISIBLE else View.GONE
        btnTakePicture.setImageResource(if (fileType == TakeCameraFileType.PHOTO) R.drawable.ic_camera_take_photo_v2 else R.drawable.ic_camera_take_video)
        takeVideoTimeLayout.setBackgroundResource(0)
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.take_picture_or_video_btn -> {
                val isTaking = iTakeCamera?.startTakeCapture(cameraPreview)
                if (fileType == TakeCameraFileType.VIDEO) {
                    takeVideoTimeLayout.setBackgroundResource(R.drawable.bg_btn_red_solid_radius_4)
                    btnTakePicture.setImageResource(if (isTaking == true) R.drawable.ic_camera_take_video_ing else R.drawable.ic_camera_take_video)
                }
            }

            R.id.retake_btn -> resumeCameraPreview()
            R.id.use_photo_video_btn -> {
                if (!doubleClick()) return
                usePhotoOnClick()
            }

            R.id.cancel_camera_btn -> showCloseConfirmDialog()
            R.id.switch_flash_btn -> {
                flashState = !flashState
                cameraPreview.switchFlash(flashState)
                if (flashState) {
                    btnFlash.setImageResource(R.drawable.ic_flash_open)
                } else {
                    btnFlash.setImageResource(R.drawable.ic_flash_close)
                }
            }
        }
    }

    private fun setViewStatus(takePhotoConfirm: Boolean) {
        takePhotoTitleTv.visibility = if (!takePhotoConfirm && !TextUtils.isEmpty(takePhotoTitleTv.text)) VISIBLE else GONE
        confirmPhotoLayout.visibility = if (takePhotoConfirm) VISIBLE else GONE
        recyclerView.visibility = if (takePhotoConfirm) GONE else VISIBLE
        tabTakePhoto.visibility = if (!takePhotoConfirm && needTakeVideo) VISIBLE else GONE
        previewView.visibility = if (takePhotoConfirm) VISIBLE else GONE
        btnTakePicture.visibility = if (takePhotoConfirm) GONE else VISIBLE
        btnFlash.visibility = if (takePhotoConfirm) GONE else VISIBLE
        updateTakeVideoView(takePhotoConfirm)
        updateCameraPreview(!takePhotoConfirm)
        if (!takePhotoConfirm) {
            previewView.removeAllViews()
        }
    }

    fun onResume() {
        iTakeCamera?.onResume(cameraPreview)
    }

    fun onPause() {
        iTakeCamera?.onPause(cameraPreview)
        if (fileType == TakeCameraFileType.VIDEO) {
            btnTakePicture.setImageResource(R.drawable.ic_camera_take_video)
        }
    }

    private fun resumeCameraPreview() {
        setViewStatus(false)
    }

    fun setOnTakeFileResultCallBack(onTakeFileResultCallBack: List<OnTakeFileResultCallBack>) {
        this.onTakeFileResultCallBacks = onTakeFileResultCallBack
    }

    fun setDefaultCameraId(cameraId: Int?) {
        cameraId ?: return
        if (cameraId == -1) return
        cameraPreview.defaultCameraId = cameraId
    }

    fun setFileTag(targetParam: TakeCameraFileParam, withParam: List<TakeCameraFileParam>?) {
        if (withParam.isNullOrEmpty()) {
            setTakePhotoMode(TakeFileMode.TakeFileDefaultMode(targetParam))
            return
        }
        setTakePhotoMode(TakeFileMode.TakeFileWithTagMode(targetParam, withParam))
    }

    private fun setTakePhotoMode(mode: TakeFileMode) {
        takeFileMode = mode
        var currentPhoto: TakeCameraFileParam? = null
        when (mode) {
            is TakeFileMode.TakeFileDefaultMode -> {
                recyclerView.visibility = GONE
                currentPhoto = mode.currentFile
            }

            is TakeFileMode.TakeFileWithTagMode -> {
                val tags = mode.withParams.map {
                    it.tag
                }
                currentPhoto = mode.currentFile
                val photoTags = tags.map {
                    FileTag(
                        tag = it, isSelected = (it == currentPhoto.tag))
                }
                recyclerView.visibility = VISIBLE
                photoTagAdapter.setNewData(photoTags)
            }
        }
        updateTakeCameraTitle(currentPhoto.tag)
        onTakeVideoProcess(0, currentPhoto.video?.videoMaxDuration ?: 15)
        needTakeVideo = currentPhoto.video?.needTakeVideo ?: false
        tabTakePhoto.visibility = if (needTakeVideo) VISIBLE else GONE
    }

    private fun updateTakeCameraTitle(withTag: String?) {
        var photo: TakeCameraFileParam? = null
        takeFileMode?.apply {
            when (takeFileMode) {
                is TakeFileMode.TakeFileDefaultMode -> {
                    val mode = takeFileMode as TakeFileMode.TakeFileDefaultMode
                    photo = mode.currentFile
                }

                is TakeFileMode.TakeFileWithTagMode -> {
                    val mode = takeFileMode as TakeFileMode.TakeFileWithTagMode
                    photo = mode.withParams.find {
                        it.tag == withTag
                    }
                }
            }
        }
        photo?.apply {
            val title = if (fileType == TakeCameraFileType.PHOTO) photoTitle else videoTitle
            if (!title.isNullOrEmpty()) {
                takePhotoTitleTv.visibility = VISIBLE
                takePhotoTitleTv.text = title
            }
        }
    }

    private fun usePhotoOnClick() {
        val tag = currentPhotoWithTag()
        val data = iTakeCamera?.confirm(tag, fileResultList)
        if (data != null) {
            fileResultList = data
            val result = data.find {
                it.tag == tag
            }
            result?.apply {
                photoTagAdapter.updateCurrentHasFilesStatus(this.data != null && this.data!!.isNotEmpty())
            }

        }
        if (isSingePhoto()) {
            onAddPhotoResult()
        } else {
            resumeCameraPreview()
        }
    }

    private fun isSingePhoto(): Boolean {
        return when (takeFileMode) {
            is TakeFileMode.TakeFileDefaultMode -> (takeFileMode as TakeFileMode.TakeFileDefaultMode).currentFile.isSinglePhoto
            is TakeFileMode.TakeFileWithTagMode -> (takeFileMode as TakeFileMode.TakeFileWithTagMode).currentFile.isSinglePhoto
            else -> false
        }
    }

    private fun showCloseConfirmDialog() {
        val dialog = AlertDialog.Builder(context).create()
        dialog.apply {
            window?.setBackgroundDrawableResource(android.R.color.transparent)
            val inflater = LayoutInflater.from(context)
            val view = inflater.inflate(R.layout.dialog_close_take_photo, null)
            setView(view)
            view.findViewById<AppCompatButton>(R.id.ok_button)?.let {
                it.setOnClickListener {
                    dialog.dismiss()
                    onAddPhotoResult()
                }
            }
            view.findViewById<AppCompatButton>(R.id.cancel_button)?.let {
                it.setOnClickListener {
                    dialog.dismiss()
                }
            }
        }
        dialog.show()
    }

    fun onAddPhotoResult() {
        onTakeFileResultCallBacks.forEach { callback ->
            callback.onTakeFileResult(fileResultList)
        }
    }

    private fun currentPhotoWithTag(): String? {
        takeFileMode?.apply {
            when (takeFileMode) {
                is TakeFileMode.TakeFileDefaultMode -> return (takeFileMode as TakeFileMode.TakeFileDefaultMode).currentFile.tag
                is TakeFileMode.TakeFileWithTagMode -> return photoTagAdapter.getSelectedPhotoTag()
            }
        }
        return null
    }

    private fun videoParam(): VideoParam? {
        takeFileMode?.apply {
            when (takeFileMode) {
                is TakeFileMode.TakeFileDefaultMode -> return (takeFileMode as TakeFileMode.TakeFileDefaultMode).currentFile.video
                is TakeFileMode.TakeFileWithTagMode -> return (takeFileMode as TakeFileMode.TakeFileWithTagMode).currentFile.video
            }
        }
        return null
    }

    override fun onStartTakePhotoCapture(previewPhoto: Bitmap) {
        setViewStatus(true)
        previewView?.removeAllViews()
        val imageView = ImageView(context)
        imageView.scaleType = ImageView.ScaleType.FIT_XY
        imageView.setImageBitmap(previewPhoto)
        previewView.addView(imageView)
    }

    override fun onStartTakeVideoCapture() {
        setViewStatus(true)
        previewView?.removeAllViews()
        val path = iTakeCamera?.getPreviewPath()
        path?.apply {
            val videoPreview = VideoPreview(context)
            videoPreview.setVideoPath(path)
            previewView.addView(videoPreview)
        }
    }

    override fun onTakeVideoProcess(currentTime: Int, maxTime: Int) {
        takePhotoTimeTv.text = TimeUtil.getSecondTime(currentTime)
        takePhotoMaxTimeTv.text = TimeUtil.getSecondTime(maxTime)
    }

    interface OnTakeFileResultCallBack {

        fun onTakeFileResult(data: List<TakeFileResult>?)

        fun cancel()
    }

}

sealed interface TakeFileMode {
    data class TakeFileDefaultMode(val currentFile: TakeCameraFileParam) : TakeFileMode
    data class TakeFileWithTagMode(
        val currentFile: TakeCameraFileParam, val withParams: List<TakeCameraFileParam>
    ) : TakeFileMode
}

data class TakeFileResult(
    var data: List<FileResult>? = null,
    var tag: String? = null,
)

data class FileResult(
    var type: TakeCameraFileType? = null,
    var path: String? = null,
)

