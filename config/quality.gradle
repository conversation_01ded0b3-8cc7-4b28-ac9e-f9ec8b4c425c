apply plugin: 'checkstyle'
apply plugin: 'pmd'

// Add checkstyle, findbugs, pmd and lint to the check task.
check.dependsOn 'checkstyle','pmd','lint'

task checkstyle(type: Checkstyle) {
    configFile file("${project.rootDir}/config/quality/checkstyle/checkstyle.xml")
    configProperties.checkstyleSuppressionsPath = file("${project.rootDir}/config/quality/checkstyle/suppressions.xml").absolutePath
    source 'src'
    include '**/*.java'
    exclude '**/gen/**'
    classpath = files()
}

task pmd(type: Pmd) {
    ignoreFailures = false
    ruleSetFiles = files("${project.rootDir}/config/quality/pmd/pmd-ruleset.xml")
    ruleSets = []

    source 'src'
    include '**/*.java'
    exclude '**/gen/**'

    reports {
        xml {
            required.set(false)
            destination file("$project.buildDir/reports/pmd/pmd.xml")
        }
        html {
            required.set(true)
            destination file("$project.buildDir/reports/pmd/pmd.html")
        }
    }
}

android {
    lintOptions {
        quiet true
        abortOnError true
        xmlReport false
        htmlReport true
        lintConfig file("${project.rootDir}/config/quality/lint/lint.xml")
        htmlOutput file("$project.buildDir/reports/lint/lint-result.html")
        xmlOutput file("$project.buildDir/reports/lint/lint-result.xml")
    }
}
