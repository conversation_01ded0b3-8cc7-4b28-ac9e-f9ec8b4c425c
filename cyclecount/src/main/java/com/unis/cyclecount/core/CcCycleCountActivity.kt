package com.unis.cyclecount.core

import android.os.Bundle
import com.unis.cyclecount.core.newitem.CcNewItemWorkFragment
import com.unis.cyclecount.core.work.CcCycleCountWorkFragment
import com.unis.cyclecount.databinding.ActivityCcCycleCountBinding
import com.unis.reactivemvi.fragmentlauncher.SupportFragment
import com.unis.platform.cyclecount.model.CountProcessDataEntity
import com.unis.platform.cyclecount.model.CountTaskEntity
import com.unis.reactivemvi.mvi.ReactiveActivity
import com.unis.reactivemvi.mvi.ReactiveViewScope
import com.unis.reactivemvi.mvi.onEvent
import com.unis.reactivemvi.mvvm.kotlin.extensions.UniversalActivityParam
import com.unis.reactivemvi.mvvm.kotlin.extensions.addFragment
import com.unis.reactivemvi.mvvm.kotlin.extensions.universalParam

class CcCycleCountActivity : ReactiveActivity<CycleCountViewModel, CycleCountUiState, ActivityCcCycleCountBinding>(){

    private val containerId get() = binding.frameLayoutContainer.id

    data class Param(val countTask: CountTaskEntity): UniversalActivityParam

    override fun createViewModel(): CycleCountViewModel {
        val param = universalParam as Param
        val countProcessDataEntity = CountProcessDataEntity(param.countTask)
        return CycleCountViewModel(CycleCountDataState(countProcessDataEntity))
    }

    override fun initView(savedInstanceState: Bundle?) {
        binding.apply {
        }
        addBackPressedCallback(this@CcCycleCountActivity::handlerBackPressed)
        onWorkProcessChangedEvent()
        onNotifyPageReRenderViewEvent()
        popStepExcludeLocationFragment()
        onBackProcessToEvent()
        onClosedTaskEvent()
    }

    override fun ReactiveViewScope.subscribeToUiState() {

    }

    private fun onWorkProcessChangedEvent() = onEvent<CycleCountEvent.WorkProcessChanged> {
        when(workProcessChange) {
            WorkProcessChange.CycleCountWork -> showCycleCountWorkFragment()
            WorkProcessChange.NewItemWork -> showNewItemWorkFragment()
        }
    }

    private fun onNotifyPageReRenderViewEvent() = onEvent<CycleCountEvent.NotifyPageReRenderView> {
        getCycleCountWorkFragment()?.notifyPageReRenderView(page)
    }

    private fun popStepExcludeLocationFragment () = onEvent<CycleCountEvent.PopStepExcludeLocationFragmentAndToScanLPEvent> {
        getCycleCountWorkFragment()?.popStepExcludeLocationFragment()
        getCycleCountWorkFragment()?.showScanLpFragment()
    }

    private fun onBackProcessToEvent() = onEvent<CycleCountEvent.OnBackProcessToEvent> {
        getCycleCountWorkFragment()?.onBackProcessTo(backProcessData.processChange, backProcessData.flag)
    }

    private fun onClosedTaskEvent() = onEvent<CycleCountEvent.ClosedTask> {
        finish()
    }

    private fun showCycleCountWorkFragment() {
        addFragment(containerId = containerId, fragment = CcCycleCountWorkFragment.newInstance(), tag = CcCycleCountWorkFragment.TAG, addToBackStack = true)
    }

    private fun showNewItemWorkFragment() {
        addFragment(containerId = containerId, fragment = CcNewItemWorkFragment.newInstance(), tag = CcNewItemWorkFragment.TAG, addToBackStack = true)
    }

    private fun getCycleCountWorkFragment(): CcCycleCountWorkFragment? {
        val fragment = supportFragmentManager.findFragmentByTag(CcCycleCountWorkFragment.TAG)
        return if (fragment is CcCycleCountWorkFragment) {
            fragment
        } else null
    }

    private fun getCurrentVisibilityFragment(): SupportFragment? {
        if (supportFragmentManager.backStackEntryCount > 0) {
            val fragmentTag = supportFragmentManager.getBackStackEntryAt(supportFragmentManager.backStackEntryCount - 1).name
            val topFragment = supportFragmentManager.findFragmentByTag(fragmentTag)
            if (topFragment is SupportFragment) {
                return topFragment
            }
        }
        return null
    }

    fun handlerBackPressed(): Boolean {
        val isHandled = handlerChildFragmentBackPressed()
        if (isHandled) {
            return true
        }
        return onFinishFragment()
    }

    private fun handlerChildFragmentBackPressed(): Boolean {
        val currentVisibilityFragment = getCurrentVisibilityFragment()
        if (currentVisibilityFragment != null) {
            return currentVisibilityFragment.onBackPressed()
        }
        return false
    }

    fun onFinishFragment(): Boolean {
        val backStackEntryCount = supportFragmentManager.backStackEntryCount
        if (backStackEntryCount > 1) {
            supportFragmentManager.popBackStack()
            return true
        }
        finish()
        return false
    }
}