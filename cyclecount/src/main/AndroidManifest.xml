<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.unis.cyclecount">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-feature android:name="android.hardware.camera.autofocus" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.SYSTEM_OVERLAY_WINDOW" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />

    <application
        android:name=".CcApplication"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:largeHeap="true"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:theme="@style/appThemeStyle"
        android:usesCleartextTraffic="true">
        <!-- 适配全面屏 -->
        <meta-data
            android:name="android.max_aspect"
            android:value="2.3" />
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.file.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/update_apk_paths" />
        </provider>
        <activity
            android:name=".login.CcSplashActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".login.CcLoginActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden" />

        <activity
            android:name=".home.CcHomeActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:launchMode="singleTask" />

        <activity
            android:name=".core.CcCycleCountActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation">
        </activity>

        <activity
            android:name=".core.history.CcCountHistoryActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"/>

        <activity android:name=".home.more.task_search.TaskSearchActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"/>

    </application>

</manifest>