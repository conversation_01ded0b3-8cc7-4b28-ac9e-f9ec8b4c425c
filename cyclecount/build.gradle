def module = System.getProperty("module")
//def isModule = module == "cyclecount"
def isModule = true
if (isModule) {
    apply plugin: 'com.android.application'
} else {
    apply plugin: 'com.android.library'
}
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'

repositories {
    flatDir {
        dirs '../libs'
    }
}

static def gitVersionCode() {
    def previousVersion = System.getProperty("lastversion")
    if (previousVersion == null) {
        def cmd = 'git rev-list HEAD --count'
        int versionCode = 1
        try{
            versionCode = Integer.parseInt(cmd.execute().text.trim());
        } catch(NumberFormatException ex){
            System.out.println("not a number" + ex);
        }
        return versionCode
    } else {
        return previousVersion.toInteger() + 1
    }
}

static def gitVersionName() {
    String[] tagList = getTagList()
    def date = new Date().format('yyyyMMdd').toString()
    def envTag = System.getProperty("env") == null ? 'CycleCount' : System.getProperty("env")
    def buildTime = tagList.findAll { it.contains(envTag + '.' + date) }.size() + 1
    def version = envTag + '.' + date + '.' + buildTime
    return version
}

private static String[] getTagList() {
    def cmd = 'git pull --tags'
    cmd.execute().text.trim()
    cmd = 'git tag'
    def tags = cmd.execute().text.trim()
    def tagList = tags.split("\n")
    tagList
}

android {
    namespace "com.unis.cyclecount"
    compileSdkVersion rootProject.ext.android["compileSdkVersion"]
    buildToolsVersion rootProject.ext.android["buildToolsVersion"]

    defaultConfig {
        if (isModule) {
            applicationId "com.unis.cyclecount"
        }
        minSdkVersion rootProject.ext.android["minSdkVersion"]
        targetSdkVersion rootProject.ext.android["targetSdkVersion"]
        versionCode gitVersionCode()
        versionName gitVersionName()
        multiDexEnabled true
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    signingConfigs {
        debug {
            storeFile file("./app-cyclecount-debug.jks")
            storePassword "ccdebug"
            keyAlias "ccdebug"
            keyPassword "ccdebug"
        }
        stage {
            storeFile file("./app-cyclecount-stage.jks")
            storePassword "ccstage"
            keyAlias "ccstage"
            keyPassword "ccstage"
        }
        release {
            storeFile file("./app-cyclecount-release.jks")
            storePassword "ccrelease"
            keyAlias "ccrelease"
            keyPassword "ccrelease"
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            if (isModule) {
                signingConfig signingConfigs.release
            }
            def env = System.getProperty('env')
            switch (env) {
                case 'PROD':
                    buildConfigField("String", "DEFAULT_CYCLE_COUNT_SERVER_URL", "\"https://cyclecount.item.com/api/\"")
                    buildConfigField("String", "VERSION_NAME", "\"${gitVersionName()}\"")
                    break
                default:
                    buildConfigField("String", "DEFAULT_CYCLE_COUNT_SERVER_URL", "\"https://cyclecount.item.com/api/\"")
                    buildConfigField("String", "VERSION_NAME", "\"${gitVersionName()}\"")
                    break
            }
        }

        debug {
            if (isModule) {
                applicationIdSuffix ".debug"
                signingConfig signingConfigs.debug
            }
            if (module != null) {
                buildConfigField("String", "DEFAULT_CYCLE_COUNT_SERVER_URL", "\"https://cyclecount-web-dev.item.pub/api/\"")
            } else  {
                buildConfigField("String", "DEFAULT_CYCLE_COUNT_SERVER_URL", "\"https://cyclecount-staging.item.com/api/\"")
            }
            buildConfigField("String", "VERSION_NAME", "\"${gitVersionName()}\"")
            debuggable true
            jniDebuggable true
        }

        stage {
            if (isModule) {
                applicationIdSuffix ".stage"
                signingConfig signingConfigs.stage
            }
            buildConfigField("String", "DEFAULT_CYCLE_COUNT_SERVER_URL", "\"https://cyclecount-staging.item.com/api/\"")
            buildConfigField("String", "VERSION_NAME", "\"${gitVersionName()}\"")
            matchingFallbacks = ['debug']
        }
    }

    sourceSets {
        main {
            if (!isModule) {
                manifest.srcFile 'src/main/manifest/AndroidManifest.xml'
            } else{
                manifest.srcFile 'src/main/AndroidManifest.xml'
            }
        }
    }


    if (isModule) {
        variantFilter {
            for (taskName in project.gradle.startParameter.taskNames) {
                def buildTypeName = it.buildType.name
                def taskNameUpper = taskName.toUpperCase()
                def buildTypeNameUpper = buildTypeName.toUpperCase()
                if (taskNameUpper.contains("ASSEMBLE") && !taskNameUpper.endsWith(buildTypeNameUpper)) {
                    it.setIgnore(true)
                    println "ignore build type:" + it.buildType.name
                }
            }
        }

        applicationVariants.all { variant ->
            variant.outputs.each { output ->
                output.versionCodeOverride = gitVersionCode()
                output.versionNameOverride = gitVersionName()
                def file = new File("$projectDir/version")
                file.createNewFile()
                file.text = """{"versionName":"$versionName","versionCode":"$versionCode","name":"cyclecount-app.apk","message":"update issue"}\n"""
            }
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    lintOptions {
        disable 'InvalidPackage'
        abortOnError false
    }
    packagingOptions {
        exclude 'META-INF/services/javax.annotation.processing.Processor'
    }
    buildFeatures {
        buildConfig true
        viewBinding true
    }
    kotlinOptions {
        // Ignore @OptIn warnings, Force 'SAM conversions = class' to compatible with AspectJ
        freeCompilerArgs = ["-opt-in=kotlin.RequiresOptIn", "-Xsam-conversions=class"]
        // Enable definitely non-nullable types
        languageVersion = "1.7"
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    testImplementation rootProject.ext.dependencies["junit"]
    androidTestImplementation rootProject.ext.dependencies["test-runner"]
    androidTestImplementation rootProject.ext.dependencies["test-rules"]

    implementation project(':reactivemvi')
    implementation(name: 'date-time-picker-release', ext: 'aar')
    implementation rootProject.ext.dependencies["ahbottomnavigation"]
}